<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>INVOICE</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="UTF-8">
	<style media="all">
        @page {
			margin: 0;
			padding:0;
		}
		body{
			font-size: 0.875rem;
            font-family: Arial, sans-serif;
            font-weight: normal;
			padding:0;
			margin:0;
		}
		.gry-color *,
		.gry-color{
			color:#000;
		}
		table{
			width: 100%;
			border-collapse: collapse;
		}
		table th{
			font-weight: normal;
		}
		table.padding th{
			padding: .25rem .7rem;
		}
		table.padding td{
			padding: .25rem .7rem;
		}
		table.sm-padding td{
			padding: .1rem .7rem;
		}
		.border-bottom td,
		.border-bottom th{
			border-bottom:1px solid #eceff4;
		}
		.text-left{
			text-align:<?php echo  $text_align ?>;
		}
		.text-right{
			text-align:<?php echo  $not_text_align ?>;
		}
	</style>
</head>
<body>
	<div>

		@php
			$logo = get_setting('header_logo');
		@endphp

		<div style="background: #eceff4;padding: 1rem;">
			<table>
				<tr>
					<td>
						@if($logo != null)
							<img src="{{ uploaded_asset($logo) }}" height="30" style="display:inline-block;">
						@else
							<img src="{{ static_asset('assets/img/logo.png') }}" height="30" style="display:inline-block;">
						@endif
					</td>
					<td style="font-size: 1.5rem;" class="text-right strong">{{  translate('INVOICE') }}</td>
				</tr>
			</table>
			<table>
				<tr>
					<td style="font-size: 1rem;" class="strong">{{ get_setting('site_name') }}</td>
					<td class="text-right"></td>
				</tr>
				<tr>
					<td class="gry-color small">{{ get_setting('contact_address') }}</td>
					<td class="text-right"></td>
				</tr>
				<tr>
					<td class="gry-color small">{{  translate('Email') }}: {{ get_setting('contact_email') }}</td>
					<td class="text-right small"><span class="gry-color small">{{  translate('Order ID') }}:</span> <span class="strong">{{ $order->code }}</span></td>
				</tr>
				<tr>
					<td class="gry-color small">{{  translate('Phone') }}: {{ get_setting('contact_phone') }}</td>
					<td class="text-right small"><span class="gry-color small">{{  translate('Order Date') }}:</span> <span class=" strong">{{ date('d-m-Y', $order->date) }}</span></td>
				</tr>
				<tr>
					<td class="gry-color small"></td>
					<td class="text-right small">
                        <span class="gry-color small">
                            {{  translate('Payment method') }}:
                        </span> 
                        <span class="strong">
                            {{ translate(ucfirst(str_replace('_', ' ', $order->payment_type))) }}
                        </span>
                    </td>
				</tr>
			</table>

		</div>

		<div style="padding: 1rem;padding-bottom: 0">
            <table>
				@php
					$shipping_address = null;
					try {
						$shipping_address = json_decode($order->shipping_address);
					} catch (Exception $e) {
						$shipping_address = null;
					}
				@endphp
				<tr><td style="font-weight: bold; font-size: 0.8rem;">Bill to:</td></tr>
				<tr><td style="font-weight: bold;">{{ $shipping_address->name ?? 'N/A' }}</td></tr>
				<tr><td style="font-size: 0.8rem;">
					{{ $shipping_address->address ?? 'N/A' }},
					{{ $shipping_address->city ?? 'N/A' }},
					@if(isset($shipping_address->state)) {{ $shipping_address->state }} - @endif
					{{ $shipping_address->postal_code ?? 'N/A' }},
					{{ $shipping_address->country ?? 'N/A' }}
				</td></tr>
				<tr><td style="font-size: 0.8rem;">Email: {{ $shipping_address->email ?? 'N/A' }}</td></tr>
				<tr><td style="font-size: 0.8rem;">Phone: {{ $shipping_address->phone ?? 'N/A' }}</td></tr>
			</table>
		</div>

	    <div style="padding: 1rem;">
			<table style="width: 100%; border-collapse: collapse; padding: 0.25rem 0.7rem; text-align: left; font-size: 0.8rem;">
				<thead>
	                <tr style="background: #eceff4;">
	                    <th width="35%" style="text-align: left; padding: 0.25rem 0.7rem;">Product Name</th>
						<th width="15%" style="text-align: left; padding: 0.25rem 0.7rem;">Delivery Type</th>
	                    <th width="10%" style="text-align: left; padding: 0.25rem 0.7rem;">Qty</th>
	                    <th width="15%" style="text-align: left; padding: 0.25rem 0.7rem;">Unit Price</th>
	                    <th width="10%" style="text-align: left; padding: 0.25rem 0.7rem;">Tax</th>
	                    <th width="15%" style="text-align: right; padding: 0.25rem 0.7rem;">Total</th>
	                </tr>
				</thead>
				<tbody class="strong">
	                @foreach ($order->orderDetails as $key => $orderDetail)
		                @if ($orderDetail->product != null)
							<tr>
								<td>
                                    {{ $orderDetail->product->name ?? 'N/A' }}
                                    @if($orderDetail->variation) ({{ $orderDetail->variation }}) @endif
                                </td>
								<td>
									@if ($order->shipping_type == 'home_delivery')
										Home Delivery
									@elseif ($order->shipping_type == 'pickup_point')
										Pickup Point
									@elseif ($order->shipping_type == 'carrier')
										Carrier
									@else
										Standard
									@endif
								</td>
								<td>{{ $orderDetail->quantity ?? 0 }}</td>
								<td>{{ single_price($orderDetail->quantity > 0 ? $orderDetail->price/$orderDetail->quantity : 0) }}</td>
								<td>{{ single_price($orderDetail->quantity > 0 ? $orderDetail->tax/$orderDetail->quantity : 0) }}</td>
			                    <td style="text-align: right;">{{ single_price(($orderDetail->price ?? 0) + ($orderDetail->tax ?? 0)) }}</td>
							</tr>
		                @endif
					@endforeach
	            </tbody>
			</table>
		</div>

	    <div style="padding:0 1.5rem;">
	        <table class="text-right sm-padding small strong">
	        	<thead>
	        		<tr>
	        			<th width="60%"></th>
	        			<th width="40%"></th>
	        		</tr>
	        	</thead>
		        <tbody>
			        <tr>
			            <td style="text-align: left;">
                            <div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;font-size:10px;">QR Code</div>
			            </td>
			            <td>
					        <table style="text-align: right; font-size: 0.8rem; font-weight: bold;">
						        <tbody>
							        <tr>
							            <th style="text-align: left;">Sub Total</th>
							            <td>{{ single_price($order->orderDetails->sum('price') ?? 0) }}</td>
							        </tr>
							        <tr>
							            <th style="text-align: left;">Shipping Cost</th>
							            <td>{{ single_price($order->orderDetails->sum('shipping_cost') ?? 0) }}</td>
							        </tr>
							        <tr style="border-bottom:1px solid #eceff4;">
							            <th style="text-align: left;">Total Tax</th>
							            <td>{{ single_price($order->orderDetails->sum('tax') ?? 0) }}</td>
							        </tr>
				                    <tr style="border-bottom:1px solid #eceff4;">
							            <th style="text-align: left;">Coupon Discount</th>
							            <td>{{ single_price($order->coupon_discount ?? 0) }}</td>
							        </tr>
							        <tr>
							            <th style="text-align: left; font-weight: bold;">Grand Total</th>
							            <td>{{ single_price($order->grand_total ?? 0) }}</td>
							        </tr>
						        </tbody>
						    </table>
			            </td>
			        </tr>
		        </tbody>
		    </table>
	    </div>

	</div>
</body>
</html>
